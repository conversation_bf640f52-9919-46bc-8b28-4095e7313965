import { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import '../../../styles/StatisticsPlaceholder.css';
import userGroupIcon from '../../../assets/users.svg';
import fileIcon from '../../../assets/file-text.svg';
import chartIcon from '../../../assets/chart-column-decreasing.svg';
import tichIcon from '../../../assets/tich.svg';
import dropdownIcon from '../../../assets/icon-sidebar/dropdown.svg';

// Đăng ký các components Chart.js cần thiết
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const StatisticsPlaceholder = () => {
  const [selectedDepartment, setSelectedDepartment] = useState('Phòng IT');
  const [selectedYear, setSelectedYear] = useState('2025');
  const [openDepartmentDropdown, setOpenDepartmentDropdown] = useState(false);
  const [openYearDropdown, setOpenYearDropdown] = useState(false);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.statistical-dropdown') && !event.target.closest('.statistical-year-dropdown')) {
        setOpenDepartmentDropdown(false);
        setOpenYearDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Mock data cho thống kê
  const statisticsData = {
    'Phòng IT': {
      employees: 12,
      projects: 50,
      tasks: 24,
      completionRate: 68
    },
    'Phòng Marketing': {
      employees: 8,
      projects: 25,
      tasks: 18,
      completionRate: 75
    },
    'Phòng Kế Toán': {
      employees: 6,
      projects: 15,
      tasks: 12,
      completionRate: 82
    }
  };

  // Mock data cho biểu đồ line chart
  const chartData = {
    labels: ['01-2025', '02-2025', '03-2025', '04-2025', '05-2025', '06-2025', '07-2025', '08-2025', '09-2025', '10-2025', '11-2025', '12-2025'],
    datasets: [
      {
        label: 'Tỉ lệ hoàn thành',
        data: [70, 18, 35, 20, 40, 12, 58, 15, 58, 55, 90, 45],
        borderColor: '#4F46E5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#4F46E5',
        pointBorderColor: '#4F46E5',
        pointRadius: 4,
        pointHoverRadius: 6,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#fff',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
      },
    },
    scales: {
      x: {
        grid: {
          color: '#f0f0f0',
          drawBorder: false,
        },
        ticks: {
          color: '#666',
          font: {
            size: 12,
          },
        },
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: '#f0f0f0',
          drawBorder: false,
        },
        ticks: {
          color: '#666',
          font: {
            size: 12,
          },
          callback: function(value) {
            return value;
          },
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: '#4F46E5',
        hoverBorderColor: '#4F46E5',
      },
    },
  };

  const departments = ['Phòng IT', 'Phòng Marketing', 'Phòng Kế Toán', 'Hành Chính Nhân Sự'];
  const years = ['2023', '2024', '2025'];

  const currentStats = statisticsData[selectedDepartment] || statisticsData['Phòng IT'];

  return (
    <div className="statistical-list-container">
      {/* Header */}
      <div className="statistical-header">
        <div className="statistical-title-row">
          <div className="statistical-title-with-icon">
            <img src={chartIcon} alt="chart" className="statistical-title-icon" />
            <h1 className="statistical-list-title">Thống kê công việc</h1>
          </div>
        </div>

        <div className="statistical-toolbar-row">
          <div className="statistical-toolbar-left">
            <div className={`statistical-dropdown ${openDepartmentDropdown ? 'open' : ''}`}>
              <button
                className="statistical-dropdown-btn"
                onClick={() => setOpenDepartmentDropdown(!openDepartmentDropdown)}
              >
                <span>{selectedDepartment}</span>
                <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
              </button>
              {openDepartmentDropdown && (
                <div className="statistical-dropdown-menu">
                  {departments.map(dept => (
                    <div
                      key={dept}
                      className="statistical-dropdown-item"
                      onClick={() => {
                        setSelectedDepartment(dept);
                        setOpenDepartmentDropdown(false);
                      }}
                    >
                      {dept}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="statistical-cards-wrapper">
        <div className="statistical-cards">
          <div className="stat-card">
            <div className="stat-title stat-title-flex">
              <span>Số lượng nhân sự</span>
              <img src={userGroupIcon} alt="users" className="stat-icon" />
            </div>
            <div className="stat-value">{currentStats.employees}</div>
            <div className="stat-desc">+2 từ tháng trước</div>
          </div>

          <div className="stat-card">
            <div className="stat-title stat-title-flex">
              <span>Số lượng dự án</span>
              <img src={fileIcon} alt="projects" className="stat-icon" />
            </div>
            <div className="stat-value">{currentStats.projects}</div>
            <div className="stat-desc">+1 từ tháng trước</div>
          </div>

          <div className="stat-card">
            <div className="stat-title stat-title-flex">
              <span>Số lượng công việc</span>
              <img src={chartIcon} alt="tasks" className="stat-icon" />
            </div>
            <div className="stat-value">{currentStats.tasks}</div>
            <div className="stat-desc">+1 từ tháng trước</div>
          </div>

          <div className="stat-card">
            <div className="stat-title stat-title-flex">
              <span>Tỉ lệ hoàn thành</span>
              <img src={tichIcon} alt="completion" className="stat-icon" />
            </div>
            <div className="stat-value">{currentStats.completionRate}%</div>
            <div className="stat-desc">+5 từ tháng trước</div>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="statistical-chart-section">
        <div className="statistical-chart-card">
          <div className="statistical-chart-header">
            <div className="statistical-chart-title">
              <img src={chartIcon} alt="chart" className="chart-title-icon" />
              <span>Tỉ lệ hoàn thành của phòng ban</span>
            </div>
            <div className="statistical-chart-subtitle">
              Phân bố năng lực theo thời gian cho {selectedDepartment}
            </div>
          </div>

          <div className="statistical-chart-controls">
            <div className="chart-type-label">Line</div>
            <div className={`statistical-year-dropdown ${openYearDropdown ? 'open' : ''}`}>
              <button
                className="statistical-year-dropdown-btn"
                onClick={() => setOpenYearDropdown(!openYearDropdown)}
              >
                <span>{selectedYear}</span>
                <img src={dropdownIcon} alt="dropdown" className="dropdown-icon" />
              </button>
              {openYearDropdown && (
                <div className="statistical-year-dropdown-menu">
                  {years.map(year => (
                    <div
                      key={year}
                      className="statistical-year-dropdown-item"
                      onClick={() => {
                        setSelectedYear(year);
                        setOpenYearDropdown(false);
                      }}
                    >
                      {year}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="statistical-chart-container">
            <Line data={chartData} options={chartOptions} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsPlaceholder;
