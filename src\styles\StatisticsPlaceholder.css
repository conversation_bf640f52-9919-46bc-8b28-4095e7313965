@import url('../index.css');
/* ========== HEADER STYLES ========== */
.statistical-header {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

.statistical-title-row {
  margin-bottom: 20px;
}

.statistical-title-with-icon {
  display: flex;
  align-items: center;
  gap: 12px;
}

.statistical-title-icon {
  width: 28px;
  height: 28px;
}

.statistical-list-title {
  font-size: 22px;
  font-weight: 600;
  color: #5D5D5D;
  margin: 0;
}

.statistical-toolbar-row {
  display: flex;
  justify-content: flex-start;
}

.statistical-toolbar-left {
  display: flex;
  gap: 12px;
}

/* ========== DROPDOWN STYLES ========== */
.statistical-dropdown {
  position: relative;
}

.statistical-dropdown-btn {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: space-between;
}

.statistical-dropdown-btn:hover {
  border-color: #4F46E5;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.statistical-dropdown.open .dropdown-icon,
.statistical-year-dropdown.open .dropdown-icon {
  transform: rotate(180deg);
}

.statistical-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 1000;
  margin-top: 4px;
}

.statistical-dropdown-item {
  padding: 10px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.statistical-dropdown-item:last-child {
  border-bottom: none;
}

.statistical-dropdown-item:hover {
  background-color: #f8f9fa;
}

/* ========== STATISTICS CARDS STYLES ========== */
.statistical-cards-wrapper {
  margin-bottom: 24px;
}

.statistical-cards {
  display: flex;
  gap: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 180px;
}

.stat-title {
  font-size: 15px;
  color: #888;
  margin-bottom: 12px;
  font-weight: 500;
}

.stat-title.stat-title-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.stat-icon {
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #222;
  margin-bottom: 8px;
}

.stat-desc {
  font-size: 13px;
  color: #bdbdbd;
}

/* ========== CHART SECTION STYLES ========== */
.statistical-chart-section {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  overflow: hidden;
}

.statistical-chart-card {
  padding: 24px;
}

.statistical-chart-header {
  margin-bottom: 20px;
}

.statistical-chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.chart-title-icon {
  width: 20px;
  height: 20px;
}

.statistical-chart-subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 28px;
}

.statistical-chart-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.chart-type-label {
  background: #f0f0f0;
  color: #666;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.statistical-year-dropdown {
  position: relative;
}

.statistical-year-dropdown-btn {
  background: #4F46E5;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  justify-content: space-between;
}

.statistical-year-dropdown-btn:hover {
  background: #4338CA;
}

.statistical-year-dropdown-btn .dropdown-icon {
  filter: brightness(0) invert(1);
}

.statistical-year-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  z-index: 1000;
  margin-top: 4px;
  min-width: 80px;
}

.statistical-year-dropdown-item {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  text-align: center;
}

.statistical-year-dropdown-item:hover {
  background-color: #f8f9fa;
}

.statistical-chart-container {
  height: 400px;
  width: 100%;
}

/* ========== RESPONSIVE STYLES ========== */
@media (max-width: 1200px) {
  .statistical-cards {
    flex-wrap: wrap;
  }
  
  .stat-card {
    min-width: calc(50% - 12px);
  }
}

@media (max-width: 768px) {
  .statistical-list-container {
    padding: 16px;
  }
  
  .statistical-header {
    padding: 16px;
  }
  
  .statistical-chart-card {
    padding: 16px;
  }
  
  .statistical-cards {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .statistical-chart-container {
    height: 300px;
  }
}
